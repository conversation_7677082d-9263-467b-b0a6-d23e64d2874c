{#
    Space Image Carousel Component - Flowbite Tailwind

    Parameters:
    - space: Space entity
    - images: Array of image URLs
    - size: 'small', 'medium', 'large' (default: 'medium')
    - showIndicators: boolean (default: true)
    - showControls: boolean (default: true)
#}

{% set carouselId = 'carousel-' ~ space.id ~ '-' ~ random() %}
{% set size = size|default('medium') %}
{% set showIndicators = showIndicators is defined ? showIndicators : true %}
{% set showControls = showControls is defined ? showControls : true %}

{% set sizeClasses = {
    'small': 'h-48',
    'medium': 'h-64',
    'large': 'h-80'
} %}

<div id="{{ carouselId }}" class="relative w-full" data-carousel="slide">
    <!-- Carousel wrapper -->
    <div class="relative overflow-hidden rounded-lg {{ sizeClasses[size] }}">
        {% for image in images %}
            <div class="hidden duration-700 ease-in-out" data-carousel-item{% if loop.first %}="active"{% endif %}>
                <img src="{{ image }}"
                     class="absolute block w-full -translate-x-1/2 -translate-y-1/2 top-1/2 left-1/2"
                     alt="{{ space.name }} - Image {{ loop.index }}"
                     loading="lazy">
            </div>
        {% endfor %}
    </div>

    {% if showIndicators and images|length > 1 %}
        <!-- Slider indicators -->
        <div class="absolute z-30 flex -translate-x-1/2 bottom-5 left-1/2 space-x-3 rtl:space-x-reverse">
            {% for image in images %}
                <button type="button"
                        class="w-3 h-3 rounded-full"
                        aria-current="{% if loop.first %}true{% else %}false{% endif %}"
                        aria-label="Slide {{ loop.index }}"
                        data-carousel-slide-to="{{ loop.index0 }}"></button>
            {% endfor %}
        </div>
    {% endif %}

    {% if showControls and images|length > 1 %}
        <!-- Slider controls -->
        <button type="button"
                class="absolute top-0 start-0 z-30 flex items-center justify-center h-full px-4 cursor-pointer group focus:outline-none"
                data-carousel-prev>
            <span class="inline-flex items-center justify-center w-10 h-10 rounded-full bg-white/30 dark:bg-gray-800/30 group-hover:bg-white/50 dark:group-hover:bg-gray-800/60 group-focus:ring-4 group-focus:ring-white dark:group-focus:ring-gray-800/70 group-focus:outline-none">
                <svg class="w-4 h-4 text-white dark:text-gray-800 rtl:rotate-180" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                    <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 1 1 5l4 4"/>
                </svg>
                <span class="sr-only">Previous</span>
            </span>
        </button>
        <button type="button"
                class="absolute top-0 end-0 z-30 flex items-center justify-center h-full px-4 cursor-pointer group focus:outline-none"
                data-carousel-next>
            <span class="inline-flex items-center justify-center w-10 h-10 rounded-full bg-white/30 dark:bg-gray-800/30 group-hover:bg-white/50 dark:group-hover:bg-gray-800/60 group-focus:ring-4 group-focus:ring-white dark:group-focus:ring-gray-800/70 group-focus:outline-none">
                <svg class="w-4 h-4 text-white dark:text-gray-800 rtl:rotate-180" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                    <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 9 4-4-4-4"/>
                </svg>
                <span class="sr-only">Next</span>
            </span>
        </button>
    {% endif %}
</div>
