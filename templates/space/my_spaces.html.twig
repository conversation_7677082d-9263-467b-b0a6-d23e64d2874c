{% extends 'base.html.twig' %}

{% block title %}My Spaces{% endblock %}



{% block content %}
            <div class="container mx-auto">
                <div class="flex justify-between items-center mb-6">
                    <h1 class="text-2xl font-bold text-gray-900 dark:text-white">My Spaces</h1>
                    {% include 'elements/link_button.html.twig' with {
                        'text': 'Add New Space',
                        'href': path('app_space_new'),
                        'variant': 'success',
                        'icon_before': 'plus'
                    } %}
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    {% for space in spaces %}
                        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden">
                            <!-- Space Image Carousel -->
                            {% set spaceImages = imageService.getSpaceImages(space) %}
                            {% include 'components/space_carousel.html.twig' with {
                                'space': space,
                                'images': spaceImages,
                                'size': 'small',
                                'showIndicators': false,
                                'showControls': imageService.hasMultipleImages(space)
                            } %}

                            <div class="p-5">
                                <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-2">{{ space.name }}</h2>
                                <p class="text-gray-700 dark:text-gray-300 mb-4">{{ space.description|slice(0, 100) }}{% if space.description|length > 100 %}...{% endif %}</p>
                                <div class="flex flex-wrap gap-2 mb-4">
                                    <span class="px-2 py-1 bg-blue-100 text-blue-800 text-xs font-medium rounded dark:bg-blue-900 dark:text-blue-300">
                                        {{ space.desks|length }} Desks
                                    </span>
                                    <span class="px-2 py-1 bg-gray-100 text-gray-800 text-xs font-medium rounded dark:bg-gray-700 dark:text-gray-300">
                                        {{ space.address.city }}
                                    </span>
                                </div>
                                <div class="flex justify-end">
                                    <a href="{{ path('app_space_show', {'id': space.id}) }}" class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 dark:bg-blue-500 dark:hover:bg-blue-600 transition-colors shadow-sm">
                                        View Details
                                    </a>
                                </div>
                            </div>
                        </div>
                    {% else %}
                        <div class="col-span-full text-center py-8">
                            <p class="text-gray-700 dark:text-gray-300">You haven't created any spaces yet.</p>
                            <div class="mt-4">
                                {% include 'elements/link_button.html.twig' with {
                                    'text': 'Create Your First Space',
                                    'href': path('app_space_new'),
                                    'variant': 'success'
                                } %}
                            </div>
                        </div>
                    {% endfor %}
                </div>
            </div>
{% endblock %}
