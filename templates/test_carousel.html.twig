<!DOCTYPE html>
<html>
<head>
    <title>Test Carousel</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/flowbite@2.5.2/dist/flowbite.min.js"></script>
</head>
<body class="bg-gray-100">
    <div class="container mx-auto p-8">
        <h1 class="text-2xl font-bold mb-8">Test Space Carousel</h1>
        
        <!-- Test Carousel -->
        <div id="test-carousel" class="relative w-full h-64" data-carousel="slide">
            <!-- Carousel wrapper -->
            <div class="relative overflow-hidden rounded-lg h-64">
                <div class="block duration-700 ease-in-out" data-carousel-item data-carousel-item-active>
                    <img src="https://source.unsplash.com/800x600/?office&sig=1" 
                         class="absolute block w-full h-64 object-cover -translate-x-1/2 -translate-y-1/2 top-1/2 left-1/2" 
                         alt="Test Image 1">
                </div>
                <div class="hidden duration-700 ease-in-out" data-carousel-item>
                    <img src="https://source.unsplash.com/800x600/?coworking&sig=2" 
                         class="absolute block w-full h-64 object-cover -translate-x-1/2 -translate-y-1/2 top-1/2 left-1/2" 
                         alt="Test Image 2">
                </div>
                <div class="hidden duration-700 ease-in-out" data-carousel-item>
                    <img src="https://source.unsplash.com/800x600/?workspace&sig=3" 
                         class="absolute block w-full h-64 object-cover -translate-x-1/2 -translate-y-1/2 top-1/2 left-1/2" 
                         alt="Test Image 3">
                </div>
            </div>
            
            <!-- Slider indicators -->
            <div class="absolute z-30 flex space-x-3 -translate-x-1/2 bottom-5 left-1/2">
                <button type="button" class="w-3 h-3 rounded-full bg-white" aria-current="true" aria-label="Slide 1" data-carousel-slide-to="0"></button>
                <button type="button" class="w-3 h-3 rounded-full bg-white/50" aria-current="false" aria-label="Slide 2" data-carousel-slide-to="1"></button>
                <button type="button" class="w-3 h-3 rounded-full bg-white/50" aria-current="false" aria-label="Slide 3" data-carousel-slide-to="2"></button>
            </div>
            
            <!-- Slider controls -->
            <button type="button" class="absolute top-0 left-0 z-30 flex items-center justify-center h-full px-4 cursor-pointer group focus:outline-none" data-carousel-prev>
                <span class="inline-flex items-center justify-center w-10 h-10 rounded-full bg-white/30 group-hover:bg-white/50 group-focus:ring-4 group-focus:ring-white group-focus:outline-none">
                    <svg class="w-4 h-4 text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 1 1 5l4 4"/>
                    </svg>
                    <span class="sr-only">Previous</span>
                </span>
            </button>
            <button type="button" class="absolute top-0 right-0 z-30 flex items-center justify-center h-full px-4 cursor-pointer group focus:outline-none" data-carousel-next>
                <span class="inline-flex items-center justify-center w-10 h-10 rounded-full bg-white/30 group-hover:bg-white/50 group-focus:ring-4 group-focus:ring-white group-focus:outline-none">
                    <svg class="w-4 h-4 text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 9 4-4-4-4"/>
                    </svg>
                    <span class="sr-only">Next</span>
                </span>
            </button>
        </div>
        
        <div class="mt-8">
            <p class="text-gray-600">Si vous voyez un carousel avec 3 images qui changent, alors le problème est dans l'intégration Symfony.</p>
            <p class="text-gray-600">Si le carousel ne fonctionne pas, alors le problème est avec Flowbite.</p>
        </div>
    </div>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM loaded');
            console.log('Flowbite available:', typeof window.Carousel !== 'undefined');
            
            // Initialize carousel manually if needed
            if (typeof window.Carousel !== 'undefined') {
                const carousel = document.getElementById('test-carousel');
                const items = [
                    { position: 0, el: carousel.querySelector('[data-carousel-item]:nth-child(1)') },
                    { position: 1, el: carousel.querySelector('[data-carousel-item]:nth-child(2)') },
                    { position: 2, el: carousel.querySelector('[data-carousel-item]:nth-child(3)') }
                ];
                
                const carouselInstance = new window.Carousel(carousel, items, {
                    defaultPosition: 0,
                    interval: 3000
                });
                
                console.log('Carousel initialized:', carouselInstance);
            }
        });
    </script>
</body>
</html>
