<?php

namespace App\Service;

use App\Entity\Space;

class ImageService
{
    private const MAX_IMAGES_PER_SPACE = 3;
    private const IMAGE_WIDTH = 800;
    private const IMAGE_HEIGHT = 600;

    // Different office/workspace themes for variety
    private const IMAGE_THEMES = [
        'office',
        'coworking',
        'workspace',
        'meeting-room',
        'business',
        'desk',
        'conference',
        'modern-office',
        'startup'
    ];

    /**
     * Get placeholder images for a space (max 3)
     * Uses Unsplash for high-quality office/workspace images
     * Uses space ID to ensure consistent images for each space
     *
     * @param Space $space
     * @return array Array of image URLs
     */
    public function getSpaceImages(Space $space): array
    {
        $spaceId = $space->getId();
        $images = [];

        // Use space ID to generate consistent image selection
        for ($i = 0; $i < self::MAX_IMAGES_PER_SPACE; $i++) {
            $themeIndex = ($spaceId + $i) % count(self::IMAGE_THEMES);
            $theme = self::IMAGE_THEMES[$themeIndex];
            $seed = $spaceId * 100 + $i; // Ensure different images for same space

            // Using Unsplash with specific office/workspace themes
            $images[] = sprintf(
                'https://source.unsplash.com/%dx%d/?%s&sig=%d',
                self::IMAGE_WIDTH,
                self::IMAGE_HEIGHT,
                $theme,
                $seed
            );
        }

        return $images;
    }

    /**
     * Get the main image for a space (first image)
     *
     * @param Space $space
     * @return string Image URL
     */
    public function getSpaceMainImage(Space $space): string
    {
        $images = $this->getSpaceImages($space);
        return $images[0] ?? $this->getDefaultImage();
    }

    /**
     * Get default placeholder image
     *
     * @return string Default image URL
     */
    public function getDefaultImage(): string
    {
        return sprintf(
            'https://source.unsplash.com/%dx%d/?office&sig=default',
            self::IMAGE_WIDTH,
            self::IMAGE_HEIGHT
        );
    }

    /**
     * Check if space has multiple images for carousel
     *
     * @param Space $space
     * @return bool
     */
    public function hasMultipleImages(Space $space): bool
    {
        return count($this->getSpaceImages($space)) > 1;
    }
}
