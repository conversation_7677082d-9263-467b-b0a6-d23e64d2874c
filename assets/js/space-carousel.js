/**
 * Space Carousel JavaScript - Simplified for Flowbite
 * Relies on Flowbite's automatic initialization
 */

// Flowbite will automatically initialize carousels with data-carousel="slide"
// We just need to ensure it's re-initialized on Turbo navigation

document.addEventListener('turbo:load', function() {
    // Re-initialize Flowbite components after Turbo navigation
    if (typeof window.initFlowbite === 'function') {
        window.initFlowbite();
    }
});

// Optional: Add some debugging
document.addEventListener('DOMContentLoaded', function() {
    console.log('Space carousel script loaded');
    console.log('Flowbite available:', typeof window.Carousel !== 'undefined');
});
