/**
 * Space Carousel JavaScript
 * Handles initialization and management of space image carousels using Flowbite
 */

document.addEventListener('DOMContentLoaded', function() {
    initializeSpaceCarousels();
});

// Also initialize on Turbo navigation
document.addEventListener('turbo:load', function() {
    initializeSpaceCarousels();
});

function initializeSpaceCarousels() {
    // Find all carousel elements
    const carousels = document.querySelectorAll('[data-carousel="slide"]');
    
    carousels.forEach(carousel => {
        // Skip if already initialized
        if (carousel.hasAttribute('data-carousel-initialized')) {
            return;
        }
        
        // Mark as initialized
        carousel.setAttribute('data-carousel-initialized', 'true');
        
        // Initialize Flowbite carousel if available
        if (typeof window.Carousel !== 'undefined') {
            const items = [];
            const carouselItems = carousel.querySelectorAll('[data-carousel-item]');
            
            carouselItems.forEach((item, index) => {
                items.push({
                    position: index,
                    el: item
                });
            });
            
            if (items.length > 0) {
                const options = {
                    defaultPosition: 0,
                    interval: 5000, // Auto-slide every 5 seconds
                    indicators: {
                        activeClasses: 'bg-white',
                        inactiveClasses: 'bg-white/50 hover:bg-white/75',
                        items: []
                    }
                };
                
                // Add indicators if they exist
                const indicators = carousel.querySelectorAll('[data-carousel-slide-to]');
                indicators.forEach((indicator, index) => {
                    options.indicators.items.push({
                        position: index,
                        el: indicator
                    });
                });
                
                // Create carousel instance
                const carouselInstance = new window.Carousel(carousel, items, options);
                
                // Handle previous button
                const prevButton = carousel.querySelector('[data-carousel-prev]');
                if (prevButton) {
                    prevButton.addEventListener('click', () => {
                        carouselInstance.prev();
                    });
                }
                
                // Handle next button
                const nextButton = carousel.querySelector('[data-carousel-next]');
                if (nextButton) {
                    nextButton.addEventListener('click', () => {
                        carouselInstance.next();
                    });
                }
                
                // Handle indicator clicks
                indicators.forEach((indicator, index) => {
                    indicator.addEventListener('click', () => {
                        carouselInstance.slideTo(index);
                    });
                });
            }
        }
    });
}

// Pause carousel on hover (optional enhancement)
document.addEventListener('mouseenter', function(e) {
    if (e.target.closest('[data-carousel="slide"]')) {
        const carousel = e.target.closest('[data-carousel="slide"]');
        carousel.setAttribute('data-carousel-paused', 'true');
    }
}, true);

document.addEventListener('mouseleave', function(e) {
    if (e.target.closest('[data-carousel="slide"]')) {
        const carousel = e.target.closest('[data-carousel="slide"]');
        carousel.removeAttribute('data-carousel-paused');
    }
}, true);
