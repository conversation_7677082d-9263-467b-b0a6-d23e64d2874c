/**
 * Reservation Date Picker JavaScript
 * Handles date picker initialization for reservation forms
 */

document.addEventListener('DOMContentLoaded', function() {
    initializeDatePickers();
});

// Also initialize on Turbo navigation
document.addEventListener('turbo:load', function() {
    initializeDatePickers();
});

function initializeDatePickers() {
    // Find all date picker inputs
    const dateInputs = document.querySelectorAll('input[type="text"][data-datepicker]');
    
    dateInputs.forEach(input => {
        // Skip if already initialized
        if (input.hasAttribute('data-datepicker-initialized')) {
            return;
        }
        
        // Mark as initialized
        input.setAttribute('data-datepicker-initialized', 'true');
        
        // Get booked dates from data attribute
        const bookedDatesStr = input.getAttribute('data-booked-dates');
        let bookedDates = [];
        
        if (bookedDatesStr) {
            try {
                bookedDates = JSON.parse(bookedDatesStr);
            } catch (e) {
                console.warn('Could not parse booked dates:', bookedDatesStr);
            }
        }
        
        // Initialize Flatpickr if available
        if (typeof flatpickr !== 'undefined') {
            flatpickr(input, {
                dateFormat: "Y-m-d",
                minDate: "today",
                disable: bookedDates,
                locale: {
                    firstDayOfWeek: 1 // Monday
                },
                onChange: function(selectedDates, dateStr, instance) {
                    // Update hidden input if exists
                    const hiddenInput = input.parentNode.querySelector('input[type="hidden"]');
                    if (hiddenInput) {
                        hiddenInput.value = dateStr;
                    }
                }
            });
        }
    });
}
